# EmoBi

This repository contains the source code for our paper "Enhancing Hyperbole and Metaphor Detection with Their Bidirectional Dynamic Interaction and Emotion Knowledge".

**Note: This version has been updated to use vLLM for local model deployment instead of Ollama.**

## Setup

### 1. Build Environment
```bash
cd EmoBi
# use anaconda to build environment
conda create -n EmoBi python=3.10
conda activate EmoBi
# install packages
pip install -r requirements.txt
```

### 2. Deploy Model with vLLM

Before running the main script, you need to start a vLLM server with your chosen model.

#### Option 1: Using Llama-2-7b-chat-hf (default)
```bash
# Install vLLM if not already installed
pip install vllm

# Start vLLM server (in a separate terminal)
python -m vllm.entrypoints.openai.api_server \
    --model meta-llama/Llama-2-7b-chat-hf \
    --host localhost \
    --port 8000
```

#### Option 2: Using other models
You can use other supported models by changing the model name in `config.py` and starting the vLLM server with the corresponding model:

```bash
# Example with Llama-3-8B
python -m vllm.entrypoints.openai.api_server \
    --model meta-llama/Meta-Llama-3-8B-Instruct \
    --host localhost \
    --port 8000
```

Then update `config.py`:
```python
MODEL_NAME = "meta-llama/Meta-Llama-3-8B-Instruct"
```

### 3. Configuration

Edit `config.py` to adjust settings:
- `MODEL_NAME`: The model name (must match the model loaded in vLLM)
- `VLLM_HOST`: vLLM server host (default: localhost)
- `VLLM_PORT`: vLLM server port (default: 8000)
- `TEMPERATURE`: Model temperature (default: 0.0)
- `MAX_TOKENS`: Maximum tokens per response (default: 1024)

## Quick Start

1. **Start vLLM server** (in a separate terminal):
```bash
python -m vllm.entrypoints.openai.api_server \
    --model meta-llama/Llama-2-7b-chat-hf \
    --host localhost \
    --port 8000
```

2. **Run the main script**:
```bash
python main.py
```

## Migration from Ollama

This codebase has been migrated from Ollama to vLLM for better performance and flexibility. Key changes:

- **Dependencies**: Added `vllm`, `openai`, and `requests` to requirements.txt
- **Model Interface**: Replaced `ollama.chat()` calls with OpenAI-compatible API calls to vLLM
- **Configuration**: Updated config.py with vLLM-specific settings
- **Deployment**: Models are now served via vLLM's OpenAI-compatible API server

## Troubleshooting

1. **Connection Error**: Make sure the vLLM server is running and accessible at the configured host:port
2. **Model Loading Issues**: Ensure you have sufficient GPU memory for the chosen model
3. **API Errors**: Check that the model name in config.py matches the model loaded in vLLM
