#!/usr/bin/env python3
"""
测试vLLM连接的简单脚本
在运行主程序之前，使用此脚本验证vLLM服务器是否正常工作
"""

import openai
import requests
from config import VLLM_API_BASE, MODEL_NAME, TEMPERATURE, MAX_TOKENS

def test_vllm_server():
    """测试vLLM服务器是否可访问"""
    try:
        # 测试服务器健康状态
        health_url = VLLM_API_BASE.replace('/v1', '/health')
        response = requests.get(health_url, timeout=5)
        if response.status_code == 200:
            print("✓ vLLM服务器健康检查通过")
            return True
        else:
            print(f"✗ vLLM服务器健康检查失败: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ 无法连接到vLLM服务器: {e}")
        return False

def test_model_inference():
    """测试模型推理功能"""
    try:
        client = openai.OpenAI(
            api_key="EMPTY",
            base_url=VLLM_API_BASE,
        )
        
        test_message = "Hello, how are you?"
        print(f"发送测试消息: {test_message}")
        
        response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[{"role": "user", "content": test_message}],
            temperature=TEMPERATURE,
            max_tokens=MAX_TOKENS,
            stream=False
        )
        
        result = response.choices[0].message.content
        print(f"✓ 模型响应: {result}")
        return True
        
    except Exception as e:
        print(f"✗ 模型推理测试失败: {e}")
        return False

def main():
    print("=== vLLM连接测试 ===")
    print(f"API Base: {VLLM_API_BASE}")
    print(f"Model: {MODEL_NAME}")
    print()
    
    # 测试服务器连接
    if not test_vllm_server():
        print("\n请确保vLLM服务器正在运行:")
        print("python -m vllm.entrypoints.openai.api_server \\")
        print(f"    --model {MODEL_NAME} \\")
        print("    --host localhost \\")
        print("    --port 8000")
        return
    
    # 测试模型推理
    if test_model_inference():
        print("\n✓ 所有测试通过！可以运行主程序了。")
    else:
        print("\n✗ 模型推理测试失败，请检查模型配置。")

if __name__ == "__main__":
    main()
